<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />

    <!-- Performance optimizations -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//fonts.gstatic.com">

    <!-- Critical CSS for above-the-fold content -->
    <style>
      /* Reset and base styles */
      * { box-sizing: border-box; }
      body {
        margin: 0;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        line-height: 1.6;
        color: #333;
        background: #fff;
      }

      /* Critical navbar styles */
      .navbar {
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 1000;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid #e5e7eb;
        transition: transform 0.3s ease;
      }

      .nav-container {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 1.5rem;
      }

      .nav-name {
        font-weight: 700;
        font-size: 1.125rem;
        color: #3b82f6;
      }

      /* Critical hero styles */
      .hero {
        padding-top: 120px;
        min-height: 90vh;
        display: flex;
        align-items: center;
        background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #581c87 100%);
        position: relative;
        overflow: hidden;
      }

      .hero-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1.5rem;
        position: relative;
        z-index: 2;
      }

      .hero-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        align-items: center;
      }

      /* Loading states */
      .loading-fallback {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 200px;
        color: #6b7280;
      }

      /* Mobile responsive */
      @media (max-width: 768px) {
        .hero-content { grid-template-columns: 1fr; text-align: center; }
        .nav-container { padding: 0.75rem 1rem; }
      }
    </style>

    <!-- Primary Meta Tags -->
    <title>Nicholas Gerasimatos - Principal Technical Consultant</title>
    <meta name="title" content="Nicholas Gerasimatos - Principal Technical Consultant" />
    <meta name="description" content="Principal Technical Consultant specializing in Cloud Architecture, AI/ML Integration, and Platform Engineering. 15+ years experience delivering $10M+ business value through innovative solutions." />
    <meta name="keywords" content="Cloud Architecture, AI/ML Integration, Platform Engineering, Digital Transformation, AWS, Red Hat, Technical Consultant" />
    <meta name="author" content="Nicholas Gerasimatos" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://nicholas-gerasimatos.netlify.app/" />
    <meta property="og:title" content="Nicholas Gerasimatos - Principal Technical Consultant" />
    <meta property="og:description" content="Principal Technical Consultant specializing in Cloud Architecture, AI/ML Integration, and Platform Engineering. 15+ years experience delivering $10M+ business value." />
    <meta property="og:image" content="https://nicholas-gerasimatos.netlify.app/vite.svg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://nicholas-gerasimatos.netlify.app/" />
    <meta property="twitter:title" content="Nicholas Gerasimatos - Principal Technical Consultant" />
    <meta property="twitter:description" content="Principal Technical Consultant specializing in Cloud Architecture, AI/ML Integration, and Platform Engineering." />
    <meta property="twitter:image" content="https://nicholas-gerasimatos.netlify.app/vite.svg" />

    <!-- Additional SEO -->
    <meta name="robots" content="index, follow" />
    <meta name="language" content="English" />
    <link rel="canonical" href="https://nicholas-gerasimatos.netlify.app/" />

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- iOS PWA Support -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Nicholas Resume" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />

    <!-- Windows PWA Support -->
    <meta name="msapplication-TileColor" content="#3b82f6" />
    <meta name="msapplication-config" content="/browserconfig.xml" />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
