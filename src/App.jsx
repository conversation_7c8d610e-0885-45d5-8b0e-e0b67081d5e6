import React, { useEffect } from 'react'
import Navbar from './components/Navbar.jsx'
import Hero from './components/Hero.jsx'
import About from './components/About.jsx'
import Featured from './components/Featured.jsx'
import ProgressiveLoader from './components/ProgressiveLoader.jsx'
import DeviceTestRunner from './components/DeviceTestRunner.jsx'
import { loadable, addResourceHints, measureWebVitals } from './utils/loadable.js'

// Code split non-critical components
const Experience = loadable(() => import('./components/Experience.jsx'))
const Skills = loadable(() => import('./components/Skills.jsx'))
const Education = loadable(() => import('./components/Education.jsx'))
const Publications = loadable(() => import('./components/Publications.jsx'))
const Contact = loadable(() => import('./components/Contact.jsx'))
const Footer = loadable(() => import('./components/Footer.jsx'))

function App() {
  useEffect(() => {
    // Add resource hints for better performance
    addResourceHints();

    // Measure web vitals in production
    if (import.meta.env.PROD) {
      measureWebVitals();
    }
  }, []);

  return (
    <div className="App">
      {/* Skip link for accessibility */}
      <a href="#main-content" className="skip-link">
        Skip to main content
      </a>

      <Navbar />

      <main id="main-content" role="main" aria-label="Main content">
        <Hero />
        <About />
        <Featured />

        {/* Progressive loading for below-the-fold content */}
        <ProgressiveLoader>
          <Experience />
        </ProgressiveLoader>

        <ProgressiveLoader>
          <Skills />
        </ProgressiveLoader>

        <ProgressiveLoader>
          <Education />
        </ProgressiveLoader>

        <ProgressiveLoader>
          <Publications />
        </ProgressiveLoader>

        <ProgressiveLoader>
          <Contact />
        </ProgressiveLoader>
      </main>

      <Footer />

      {/* Development tools */}
      {import.meta.env.DEV && <DeviceTestRunner />}
    </div>
  )
}

export default App
