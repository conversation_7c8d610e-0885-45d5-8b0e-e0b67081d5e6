

:root {
  /* Colors */
  --color-background: #0d1b2aff;
  --color-surface: #1b263bff;
  --color-text: #e0e1dd;
  --color-text-secondary: #e0e1dd;
  --color-primary: #415a77ff;
  --color-primary-hover: #778da9ff;
  --color-primary-active: #e0e1dd;
  --color-secondary: #415a77ff;
  --color-secondary-hover: #778da9ff;
  --color-secondary-active: #e0e1dd;
  --color-border: #415a77ff;
  --color-btn-primary-text: #e0e1dd;
  --color-card-border: #415a77ff;
  --color-card-border-inner: #415a77ff;
  --color-error: rgba(192, 21, 47, 1);
  --color-success: rgba(33, 128, 141, 1);
  --color-warning: rgba(168, 75, 47, 1);
  --color-info: rgba(98, 108, 113, 1);
  --color-focus-ring: rgba(33, 128, 141, 0.4);
  --color-select-caret: rgba(19, 52, 59, 0.8);

  /* Common style patterns */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for opacity control */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;

  /* Typography */
  --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, monospace;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;

  /* Spacing */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04),
    0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04),
    0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);

  /* Layout */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1200px;

  /* LinkedIn-inspired colors */
  --color-linkedin: #0077b5;
  --color-linkedin-hover: #005885;
  --color-success-light: #d4edda;
  --color-warning-light: #fff3cd;
  --color-info-light: #d1ecf1;
  --color-gradient-primary: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-hover) 100%);
  --color-gradient-hero: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #581c87 100%);
  
  /* Enhanced shadows */
  --shadow-card: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-card-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-hero: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --container-lg: 1024px;
  --container-xl: 1280px;
}







/* Base styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 {
  font-size: var(--font-size-4xl);
}
h2 {
  font-size: var(--font-size-3xl);
}
h3 {
  font-size: var(--font-size-2xl);
}
h4 {
  font-size: var(--font-size-xl);
}
h5 {
  font-size: var(--font-size-lg);
}
h6 {
  font-size: var(--font-size-md);
}

p {
  margin: 0 0 var(--space-16) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

code,
pre {
  font-family: var(--font-family-mono);
  font-size: calc(var(--font-size-base) * 0.95);
  background-color: var(--color-secondary);
  border-radius: var(--radius-sm);
}

code {
  padding: var(--space-1) var(--space-4);
}

pre {
  padding: var(--space-16);
  margin: var(--space-16) 0;
  overflow: auto;
  border: 1px solid var(--color-border);
}

pre code {
  background: none;
  padding: 0;
}

/* Enhanced Buttons for Touch and Desktop */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-12) var(--space-20);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  border: none;
  text-decoration: none;
  position: relative;
  min-height: 44px; /* Minimum touch target size */
  white-space: nowrap;
  overflow: hidden;
}

/* Touch-friendly hover states for devices that support hover */
@media (hover: hover) and (pointer: fine) {
  .btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }
}

/* Active state for all devices */
.btn:active {
  transform: translateY(0);
  transition-duration: var(--duration-fast);
}

.btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
}

.btn--primary:active {
  background: var(--color-primary-active);
}

.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
}

.btn--secondary:hover {
  background: var(--color-secondary-hover);
}

.btn--secondary:active {
  background: var(--color-secondary-active);
}

.btn--outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.btn--outline:hover {
  background: var(--color-secondary);
}

.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Enhanced Form elements for mobile and desktop */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-12) var(--space-16);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: border-color var(--duration-fast) var(--ease-standard),
    box-shadow var(--duration-fast) var(--ease-standard);
  min-height: 44px; /* Touch-friendly minimum height */
  -webkit-appearance: none; /* Remove iOS styling */
  -moz-appearance: none;
  appearance: none;
}

textarea.form-control {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
}

select.form-control {
  padding: var(--space-8) var(--space-12);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  background-size: 16px;
  padding-right: var(--space-32);
}





.form-control:focus {
  border-color: var(--color-primary);
  outline: var(--focus-outline);
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.1);
}

/* Enhanced focus for mobile devices */
@media (max-width: 768px) {
  .form-control:focus {
    transform: scale(1.02);
    transition: all var(--duration-normal) var(--ease-standard);
  }
}

.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Enhanced form elements */
.btn--loading {
  position: relative;
  color: transparent !important;
}

.btn-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.form-message {
  margin-top: var(--space-16);
  padding: var(--space-12) var(--space-16);
  border-radius: var(--radius-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.form-message--success {
  background-color: var(--color-success-light);
  color: var(--color-success);
  border: 1px solid var(--color-success);
}

.form-message--error {
  background-color: var(--color-error-light);
  color: var(--color-error);
  border: 1px solid var(--color-error);
}

/* Card component */
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card__body {
  padding: var(--space-16);
}

.card__header,
.card__footer {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

/* Status indicators - simplified with CSS variables */
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.status--success {
  background-color: rgba(
    var(--color-success-rgb, 33, 128, 141),
    var(--status-bg-opacity)
  );
  color: var(--color-success);
  border: 1px solid
    rgba(var(--color-success-rgb, 33, 128, 141), var(--status-border-opacity));
}

.status--error {
  background-color: rgba(
    var(--color-error-rgb, 192, 21, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-error);
  border: 1px solid
    rgba(var(--color-error-rgb, 192, 21, 47), var(--status-border-opacity));
}

.status--warning {
  background-color: rgba(
    var(--color-warning-rgb, 168, 75, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-warning);
  border: 1px solid
    rgba(var(--color-warning-rgb, 168, 75, 47), var(--status-border-opacity));
}

.status--info {
  background-color: rgba(
    var(--color-info-rgb, 98, 108, 113),
    var(--status-bg-opacity)
  );
  color: var(--color-info);
  border: 1px solid
    rgba(var(--color-info-rgb, 98, 108, 113), var(--status-border-opacity));
}

/* Enhanced Container layout with more breakpoints */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
}

/* Extra small devices (phones, 576px and up) */
@media (min-width: 576px) {
  .container {
    max-width: 540px;
    padding-right: var(--space-20);
    padding-left: var(--space-20);
  }
}

/* Small devices (landscape phones, 640px and up) */
@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
    padding-right: var(--space-24);
    padding-left: var(--space-24);
  }
}

/* Large devices (desktops, 1024px and up) */
@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}

/* Extra large devices (large desktops, 1280px and up) */
@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Ultra wide devices (1440px and up) */
@media (min-width: 1440px) {
  .container {
    max-width: 1320px;
  }
}

/* Utility classes */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-4 {
  gap: var(--space-4);
}
.gap-8 {
  gap: var(--space-8);
}
.gap-16 {
  gap: var(--space-16);
}

.m-0 {
  margin: 0;
}
.mt-8 {
  margin-top: var(--space-8);
}
.mb-8 {
  margin-bottom: var(--space-8);
}
.mx-8 {
  margin-left: var(--space-8);
  margin-right: var(--space-8);
}
.my-8 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}
.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}
.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}
.py-16 {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}
.px-16 {
  padding-left: var(--space-16);
  padding-right: var(--space-16);
}

.block {
  display: block;
}
.hidden {
  display: none;
}

/* Enhanced Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Enhanced focus styles */
:focus-visible {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* High contrast focus for better visibility */
@media (prefers-contrast: high) {
  :focus-visible {
    outline: 3px solid #000;
    outline-offset: 2px;
  }
}

/* Skip link for keyboard navigation */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 10000;
  transition: top 0.3s;
}

.skip-link:focus {
  top: 6px;
}

/* Improved button accessibility */
button:focus-visible,
.btn:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Enhanced link accessibility */
a:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: 2px;
}

/* Form accessibility improvements */
.form-control:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 1px;
}

/* Screen reader improvements */
.visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Keyboard navigation improvements */
.nav-link:focus-visible,
.hamburger:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: var(--radius-base);
}

/* Global Image Optimization */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Responsive media */
video,
iframe,
embed,
object {
  max-width: 100%;
  height: auto;
}

/* Prevent layout shift during image loading */
img[width][height] {
  height: auto;
}

/* Optimize image rendering */
img {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}



@font-face {
  font-family: 'FKGroteskNeue';
  src: url('https://r2cdn.perplexity.ai/fonts/FKGroteskNeue.woff2')
    format('woff2');
}

/* Custom Resume Styles */

/* Performance optimizations */
html {
  scroll-behavior: smooth;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* GPU acceleration for smooth animations */
.btn,
.nav-link,
.card,
.hero-image-container,
.profile-image {
  will-change: transform;
  transform: translateZ(0);
}

/* Optimize font rendering */
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Contain layout shifts */
.hero-image-container,
.image-frame {
  contain: layout style paint;
}

/* Lazy image loading styles */
.lazy-image-container {
  position: relative;
  overflow: hidden;
}

.lazy-image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--color-surface);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  /* Reduce animations on mobile for better performance */
  .fade-in-up {
    animation-duration: 0.4s;
  }

  /* Optimize touch scrolling */
  body {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: contain;
  }

  /* Prevent zoom on input focus */
  input[type="text"],
  input[type="email"],
  input[type="tel"],
  textarea,
  select {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Optimize tap highlighting */
  * {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  }
}

/* Enhanced Navigation Styles */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: var(--color-surface);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--color-border);
  z-index: 1000;
  transition: all var(--duration-normal) var(--ease-standard);
  transform: translateY(0);
}

/* Mobile navbar hide/show on scroll */
.navbar--hidden {
  transform: translateY(-100%);
}

/* Enhanced mobile menu state */
.navbar--menu-open {
  background: rgba(var(--color-background-rgb), 0.98);
}

/* Prevent body scroll when mobile menu is open */
body.mobile-menu-open {
  overflow: hidden;
  position: fixed;
  width: 100%;
}

.nav-container {
  max-width: var(--container-xl);
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-16) var(--space-24);
}

.nav-name {
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
  color: var(--color-primary);
}

.nav-menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--space-32);
  align-items: center; /* Align items vertically */
}

.download-buttons {
  display: flex;
  gap: var(--space-8);
}

@media (max-width: 768px) {
  .nav-menu {
    flex-direction: column;
    align-items: flex-start;
  }
  .download-buttons {
    flex-direction: column;
    width: 100%;
  }
  .download-buttons .btn {
    width: 100%;
  }
}

.nav-link {
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  transition: color var(--duration-fast) var(--ease-standard);
  position: relative;
}

.nav-link:hover {
  color: var(--color-primary);
}

.nav-link.active {
  color: var(--color-primary);
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--color-primary);
  border-radius: 1px;
}

.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.hamburger span {
  width: 24px;
  height: 3px;
  background: var(--color-text);
  border-radius: 2px;
  transition: all var(--duration-normal) var(--ease-standard);
}

/* Hero Section */
.hero {
  padding-top: 120px;
  padding-bottom: var(--space-32);
  background: var(--color-background);
  min-height: 90vh;
  display: flex;
  align-items: center;
}

.hero-container {
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--space-24);
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-32);
  align-items: center;
}

.hero-title {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--space-8);
  color: var(--color-primary);
}

.hero-subtitle {
  font-size: var(--font-size-2xl);
  color: var(--color-text);
  margin-bottom: var(--space-8);
}

.hero-tagline {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-16);
  font-weight: var(--font-weight-medium);
}

.hero-description {
  font-size: var(--font-size-md);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-24);
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: var(--space-16);
}

.hero-image {
  display: flex;
  justify-content: center;
}

.hero-placeholder {
  animation: fadeInUp 1s var(--ease-standard) 0.3s both;
}

.profile-placeholder {
  filter: drop-shadow(var(--shadow-lg));
}

/* Section Styles */
.section-title {
  text-align: center;
  margin-bottom: var(--space-32);
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--color-primary);
  border-radius: 2px;
}

/* About Section */
.about {
  padding: var(--space-32) 0;
}

.about-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.about-text {
  font-size: var(--font-size-lg);
  line-height: 1.7;
  margin-bottom: var(--space-24);
  color: var(--color-text-secondary);
}

.contact-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-16);
  margin-top: var(--space-32);
  padding: var(--space-24);
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
}

.contact-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.contact-label {
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.contact-value {
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

/* Experience Section */
.experience {
  padding: var(--space-32) 0;
  background: var(--color-surface);
}

.timeline {
  position: relative;
  max-width: 900px;
  margin: 0 auto;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 30px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--color-border);
}

.timeline-item {
  position: relative;
  margin-bottom: var(--space-32);
  margin-left: 60px;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: -45px;
  top: 8px;
  width: 12px;
  height: 12px;
  background: var(--color-primary);
  border-radius: 50%;
  border: 3px solid var(--color-surface);
  box-shadow: 0 0 0 3px var(--color-border);
}

.timeline-content {
  background: var(--color-background);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  overflow: hidden;
  transition: all var(--duration-normal) var(--ease-standard);
}

.timeline-content:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.timeline-header {
  padding: var(--space-20);
  position: relative;
  cursor: pointer;
  transition: background-color var(--duration-fast) var(--ease-standard);
}

.timeline-header:hover {
  background: rgba(33, 128, 141, 0.02);
}

.job-title {
  font-size: var(--font-size-xl);
  color: var(--color-primary);
  margin-bottom: var(--space-8);
}

.company {
  display: block;
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--space-4);
}

.duration {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-4);
}

.location {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.expand-btn {
  position: absolute;
  top: var(--space-20);
  right: var(--space-20);
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--color-primary);
  color: #e0e1dd;
  border: none;
  cursor: pointer;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--duration-fast) var(--ease-standard);
}

.expand-btn:hover {
  background: var(--color-primary-hover);
  transform: scale(1.1);
}

.expand-btn.active {
  transform: rotate(45deg);
}

.timeline-details {
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--duration-normal) var(--ease-standard);
  border-top: 1px solid var(--color-card-border);
}

.timeline-details.expanded {
  max-height: 9999px; /* A large value to accommodate dynamic content */
}

.responsibilities {
  padding: var(--space-20);
  margin: 0;
  list-style: none;
}

.responsibilities li {
  margin-bottom: var(--space-12);
  padding-left: var(--space-20);
  position: relative;
  line-height: 1.6;
  color: var(--color-text-secondary);
}

.responsibilities li::before {
  content: '▸';
  position: absolute;
  left: 0;
  color: var(--color-primary);
  font-weight: var(--font-weight-bold);
}

/* Skills Section */
.skills {
  padding: var(--space-32) 0;
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-24);
  justify-content: center;
  align-items: start;
  grid-auto-rows: 1fr;
}

/* Enhanced skills grid responsiveness */
@media (min-width: 576px) {
  .skills-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

@media (min-width: 768px) {
  .skills-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-32);
  }
}

@media (min-width: 1024px) {
  .skills-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1280px) {
  .skills-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.skill-category {
  background: var(--color-surface);
  padding: var(--space-24);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  transition: all var(--duration-normal) var(--ease-standard);
}

.skill-category:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.skill-category-title {
  font-size: var(--font-size-xl);
  color: var(--color-primary);
  margin-bottom: var(--space-16);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-8);
}

.skill-category-title svg {
  font-size: var(--font-size-2xl);
  color: var(--color-text-secondary);
}

.skill-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-8);
}

.skill-tag {
  display: inline-block;
  padding: var(--space-6) var(--space-12);
  background: var(--color-secondary);
  color: var(--color-text);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--duration-fast) var(--ease-standard);
  border: 1px solid var(--color-secondary);
}

.skill-tag:hover {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
  border-color: var(--color-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-xs);
}

/* Education Section */
.education {
  padding: var(--space-32) 0;
  background: var(--color-surface);
}

.education-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-24);
}

.education-card {
  background: var(--color-background);
  padding: var(--space-24);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  transition: all var(--duration-normal) var(--ease-standard);
}

.education-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.card-title {
  font-size: var(--font-size-xl);
  color: var(--color-primary);
  margin-bottom: var(--space-16);
  text-align: center;
}

.education-item h4,
.award-item h4 {
  font-size: var(--font-size-lg);
  color: var(--color-text);
  margin-bottom: var(--space-8);
}

.education-item p,
.award-item p {
  color: var(--color-text-secondary);
  margin: 0;
}

.certification-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.certification-list li {
  padding: var(--space-8) 0;
  border-bottom: 1px solid var(--color-card-border);
  color: var(--color-text-secondary);
  position: relative;
  padding-left: var(--space-16);
}

.certification-list li:last-child {
  border-bottom: none;
}

.certification-list li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--color-primary);
  font-weight: var(--font-weight-bold);
}

/* Publications Section */
.publications {
  padding: var(--space-32) 0;
}

.publications-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-20);
}

.publication-item {
  background: var(--color-surface);
  padding: var(--space-20);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  transition: all var(--duration-normal) var(--ease-standard);
  cursor: pointer;
}

.publication-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  background: rgba(33, 128, 141, 0.02);
}

.publication-item h3 {
  color: var(--color-text);
  font-size: var(--font-size-md);
  line-height: 1.5;
  margin: 0;
  transition: color var(--duration-fast) var(--ease-standard);
}

.publication-item:hover h3 {
  color: var(--color-primary);
}

/* Contact Section */
.contact {
  padding: var(--space-32) 0;
  background: var(--color-surface);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-32);
  max-width: 1000px;
  margin: 0 auto;
}

.contact-info-section,
.contact-form-section {
  background: var(--color-background);
  padding: var(--space-24);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
}

.contact-info-section h3,
.contact-form-section h3 {
  color: var(--color-primary);
  margin-bottom: var(--space-20);
  text-align: center;
}

.contact-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
}

.contact-detail {
  padding: var(--space-12);
  background: var(--color-surface);
  border-radius: var(--radius-base);
  border: 1px solid var(--color-card-border);
}

.contact-detail strong {
  color: var(--color-text);
  display: block;
  margin-bottom: var(--space-4);
  font-size: var(--font-size-sm);
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
}

/* Footer */
.footer {
  background: var(--color-text);
  color: var(--color-background);
  text-align: center;
  padding: var(--space-24) 0;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.8s var(--ease-standard) both;
}

/* Enhanced Responsive Design */

/* Tablet breakpoint */
@media (max-width: 1024px) {
  .hero-content {
    gap: var(--space-24);
  }

  .hero-container {
    padding: 0 var(--space-20);
  }

  .nav-container {
    padding: var(--space-16) var(--space-20);
  }
}

/* Mobile breakpoint */
@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--color-surface);
    flex-direction: column;
    padding: var(--space-20);
    box-shadow: var(--shadow-lg);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all var(--duration-normal) var(--ease-standard);
    z-index: 1000;
    border-top: 1px solid var(--color-border);
  }

  .nav-menu.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .nav-menu li {
    margin: 0;
    width: 100%;
  }

  .nav-link {
    display: block;
    padding: var(--space-16) 0;
    border-bottom: 1px solid var(--color-border);
    font-size: var(--font-size-lg);
    text-align: center;
  }

  .nav-link:last-child {
    border-bottom: none;
  }

  .download-buttons {
    margin-top: var(--space-16);
    padding-top: var(--space-16);
    border-top: 1px solid var(--color-border);
    display: flex;
    flex-direction: column;
    gap: var(--space-12);
  }

  .download-buttons .btn {
    width: 100%;
    justify-content: center;
  }

  .hamburger {
    display: flex;
    cursor: pointer;
    background: none;
    border: none;
    padding: var(--space-8);
    border-radius: var(--radius-md);
    transition: background-color var(--duration-normal) var(--ease-standard);
  }

  .hamburger:hover {
    background-color: var(--color-surface-hover);
  }

  .hamburger.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }

  .hamburger.active span:nth-child(2) {
    opacity: 0;
  }

  .hamburger.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }

  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--space-24);
  }

  .hero-title {
    font-size: var(--font-size-3xl);
    line-height: 1.2;
  }

  .hero-subtitle {
    font-size: var(--font-size-xl);
    line-height: 1.3;
  }

  .hero-description {
    font-size: var(--font-size-md);
    line-height: 1.5;
    margin-bottom: var(--space-20);
  }

  .hero-buttons {
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--space-12);
  }

  .btn {
    min-height: 48px; /* Better touch target */
    padding: var(--space-12) var(--space-20);
    font-size: var(--font-size-md);
  }

  .timeline {
    margin-left: var(--space-16);
  }

  .timeline::before {
    left: 0;
  }

  .timeline-item {
    margin-left: var(--space-24);
  }

  .timeline-item::before {
    left: -27px;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: var(--space-24);
  }

  .skills-grid,
  .education-grid,
  .publications-grid {
    grid-template-columns: 1fr;
  }
}

/* Small mobile breakpoint */
@media (max-width: 480px) {
  .nav-container {
    padding: var(--space-12) var(--space-16);
  }

  .hero {
    padding-top: 100px;
    padding-bottom: var(--space-24);
  }

  .hero-container {
    padding: 0 var(--space-16);
  }

  .hero-title {
    font-size: var(--font-size-2xl);
    line-height: 1.1;
    margin-bottom: var(--space-6);
  }

  .hero-subtitle {
    font-size: var(--font-size-lg);
    line-height: 1.2;
    margin-bottom: var(--space-6);
  }

  .hero-tagline {
    font-size: var(--font-size-md);
    margin-bottom: var(--space-12);
  }

  .hero-description {
    font-size: var(--font-size-sm);
    line-height: 1.4;
    margin-bottom: var(--space-16);
  }

  .section-title {
    font-size: var(--font-size-2xl);
    line-height: 1.2;
    margin-bottom: var(--space-24);
  }

  .section {
    padding: var(--space-24) 0;
  }

  .container {
    padding-left: var(--space-12);
    padding-right: var(--space-12);
  }

  .contact-info {
    grid-template-columns: 1fr;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-10);
  }

  .btn {
    width: 100%;
    justify-content: center;
    min-height: 44px;
    font-size: var(--font-size-sm);
  }

  .form-control {
    min-height: 44px;
    font-size: var(--font-size-md);
    padding: var(--space-12);
  }

  .nav-link {
    font-size: var(--font-size-md);
    padding: var(--space-14) 0;
  }
}

/* ===== LinkedIn-Inspired Design Enhancements ===== */

/* Enhanced Hero Section */
.hero {
  position: relative;
  overflow: hidden;
}

.hero-banner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: var(--color-gradient-hero);
  z-index: 1;
}

.banner-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.hero-container {
  position: relative;
  z-index: 2;
  padding-top: 120px;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-6) var(--space-12);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  backdrop-filter: blur(10px);
  margin-bottom: var(--space-16);
}

.hero-headline {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-16);
  line-height: var(--line-height-tight);
}

.hero-value-prop {
  font-size: var(--font-size-lg);
  color: var(--color-text);
  margin-bottom: var(--space-24);
  font-weight: var(--font-weight-medium);
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--space-24);
  margin-bottom: var(--space-32);
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary-hover);
  line-height: 1;
}

.stat-label {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-top: var(--space-4);
}

.hero-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-16);
  align-items: center;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-8);
  padding: var(--space-12) var(--space-24);
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: all var(--duration-normal) var(--ease-standard);
  border: 2px solid transparent;
}

.btn--linkedin {
  background: var(--color-linkedin);
  color: white;
  border-color: var(--color-linkedin);
}

.btn--linkedin:hover {
  background: var(--color-linkedin-hover);
  border-color: var(--color-linkedin-hover);
  transform: translateY(-2px);
}

.btn-icon {
  font-size: var(--font-size-lg);
  transition: transform var(--duration-normal) var(--ease-standard);
}

.btn:hover .btn-icon {
  transform: translateX(4px);
}

.hero-image-container {
  position: relative;
  max-width: 400px;
  margin: 0 auto;
}

.image-frame {
  position: relative;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-hero);
  aspect-ratio: 1 / 1; /* Maintain square aspect ratio */
}

.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
  transition: transform var(--duration-normal) var(--ease-standard);
}

/* Responsive image optimizations */
@media (max-width: 768px) {
  .hero-image-container {
    max-width: 280px;
  }

  .image-frame {
    aspect-ratio: 1 / 1;
  }
}

@media (max-width: 480px) {
  .hero-image-container {
    max-width: 240px;
  }
}

/* Image loading optimization */
.profile-image {
  loading: lazy;
}

/* Hover effect for desktop */
@media (hover: hover) {
  .image-frame:hover .profile-image {
    transform: scale(1.05);
  }
}

.image-badge {
  position: absolute;
  bottom: var(--space-16);
  right: var(--space-16);
  background: rgba(255, 255, 255, 0.95);
  color: var(--color-background);
  padding: var(--space-8) var(--space-12);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  backdrop-filter: blur(10px);
}

/* Featured Section */
.featured {
  padding: var(--space-32) 0;
  background: white;
}

.section-subtitle {
  text-align: center;
  color: var(--color-text-secondary);
  margin-bottom: var(--space-32);
  font-size: var(--font-size-lg);
}

/* Enhanced responsive grid layouts */
.featured-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-24);
  margin-bottom: var(--space-32);
}

/* Responsive grid adjustments */
@media (min-width: 576px) {
  .featured-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

@media (min-width: 768px) {
  .featured-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-32);
  }
}

@media (min-width: 1024px) {
  .featured-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.featured-card {
  background: var(--color-background);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  transition: all var(--duration-normal) var(--ease-standard);
}

.featured-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-card-hover);
  border-color: var(--color-primary);
}

.featured-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-16);
}

.featured-icon {
  font-size: var(--font-size-3xl);
}

.featured-badge {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.featured-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--space-12);
}

.featured-description {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-16);
  line-height: var(--line-height-normal);
}

.featured-highlight {
  background: rgba(65, 90, 119, 0.1);
  border-left: 3px solid var(--color-primary);
  padding: var(--space-12);
  margin-bottom: var(--space-16);
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

.highlight-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-primary-hover);
}

.featured-link {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: color var(--duration-normal) var(--ease-standard);
}

.featured-link:hover {
  color: var(--color-primary-hover);
}

/* Social Proof */
.social-proof {
  text-align: center;
  padding: var(--space-32);
  background: rgba(65, 90, 119, 0.05);
  border-radius: var(--radius-lg);
}

.social-proof-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--space-24);
}

.company-logos {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-32);
  flex-wrap: wrap;
}

.company-logo {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-secondary);
}

.logo-image {
  height: 40px;
  width: auto;
  max-width: 120px;
  object-fit: contain;
}

/* Enhanced About Section */
.about-intro {
  margin-bottom: var(--space-32);
}

.about-highlight {
  background: var(--color-success-light);
  color: var(--color-background);
  padding: var(--space-16);
  border-radius: var(--radius-lg);
  margin-top: var(--space-16);
  font-weight: var(--font-weight-medium);
}

.value-props-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-20);
  margin-bottom: var(--space-32);
}

.value-prop-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
  text-align: center;
  transition: all var(--duration-normal) var(--ease-standard);
}

.value-prop-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-card);
  border-color: var(--color-primary);
}

.value-prop-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--space-12);
  display: block;
}

.value-prop-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--space-8);
}

.value-prop-description {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

/* Testimonials */
.testimonials-section {
  margin-bottom: var(--space-32);
}

.testimonials-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--space-20);
  text-align: center;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-20);
}

.testimonial-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
  position: relative;
}

.testimonial-card::before {
  content: '"';
  position: absolute;
  top: var(--space-12);
  left: var(--space-16);
  font-size: var(--font-size-4xl);
  color: var(--color-primary);
  opacity: 0.3;
  font-family: serif;
}

.testimonial-quote {
  font-style: italic;
  color: var(--color-text);
  margin-bottom: var(--space-16);
  padding-left: var(--space-20);
  line-height: var(--line-height-normal);
}

.testimonial-author {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.author-name {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
}

.author-role {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Enhanced Contact Info */
.contact-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-16);
  margin-top: var(--space-32);
  padding: var(--space-20);
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
}

.contact-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.contact-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
}

.contact-value {
  color: var(--color-text);
  text-decoration: none;
  transition: color var(--duration-normal) var(--ease-standard);
}

.contact-value:hover {
  color: var(--color-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-banner {
    height: 150px;
  }
  
  .hero-container {
    padding-top: 80px;
  }
  
  .hero-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-16);
  }
  
  .hero-buttons {
    flex-direction: column;
    align-items: stretch;
  }
  
  .btn {
    justify-content: center;
  }
  
  .featured-grid {
    grid-template-columns: 1fr;
  }
  
  .value-props-grid {
    grid-template-columns: 1fr;
  }
  
  .testimonials-grid {
    grid-template-columns: 1fr;
  }
  
  .company-logos {
    gap: var(--space-16);
  }
  
  .contact-info {
    grid-template-columns: 1fr;
  }
}

