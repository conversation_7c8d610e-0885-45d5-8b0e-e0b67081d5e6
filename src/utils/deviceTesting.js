// Device and browser compatibility testing utilities

export const deviceBreakpoints = {
  mobile: {
    small: { width: 320, height: 568, name: 'iPhone SE' },
    medium: { width: 375, height: 667, name: 'iPhone 8' },
    large: { width: 414, height: 896, name: 'iPhone 11 Pro Max' }
  },
  tablet: {
    portrait: { width: 768, height: 1024, name: 'iPad Portrait' },
    landscape: { width: 1024, height: 768, name: 'iPad Landscape' }
  },
  desktop: {
    small: { width: 1024, height: 768, name: 'Small Desktop' },
    medium: { width: 1440, height: 900, name: 'Medium Desktop' },
    large: { width: 1920, height: 1080, name: 'Large Desktop' },
    ultrawide: { width: 2560, height: 1440, name: 'Ultrawide' }
  }
};

export const testViewports = () => {
  const results = [];
  
  Object.entries(deviceBreakpoints).forEach(([category, devices]) => {
    Object.entries(devices).forEach(([size, config]) => {
      const test = testViewport(config);
      results.push({
        category,
        size,
        device: config.name,
        ...test
      });
    });
  });
  
  return results;
};

export const testViewport = (config) => {
  const { width, height, name } = config;
  
  // Simulate viewport change
  const originalWidth = window.innerWidth;
  const originalHeight = window.innerHeight;
  
  try {
    // Test responsive design
    const responsiveTest = testResponsiveDesign(width, height);
    
    // Test navigation
    const navigationTest = testNavigation(width);
    
    // Test content layout
    const layoutTest = testContentLayout(width);
    
    // Test performance
    const performanceTest = testPerformance();
    
    return {
      viewport: `${width}x${height}`,
      responsive: responsiveTest,
      navigation: navigationTest,
      layout: layoutTest,
      performance: performanceTest,
      passed: responsiveTest.passed && navigationTest.passed && layoutTest.passed
    };
  } catch (error) {
    return {
      viewport: `${width}x${height}`,
      error: error.message,
      passed: false
    };
  }
};

export const testResponsiveDesign = (width, height) => {
  const issues = [];
  
  // Test container max-widths
  const containers = document.querySelectorAll('.container');
  containers.forEach(container => {
    const computedStyle = window.getComputedStyle(container);
    const maxWidth = parseInt(computedStyle.maxWidth);
    
    if (width < 768 && maxWidth > width) {
      issues.push(`Container max-width (${maxWidth}px) exceeds viewport width (${width}px)`);
    }
  });
  
  // Test grid layouts
  const grids = document.querySelectorAll('.skills-grid, .featured-grid, .education-grid');
  grids.forEach(grid => {
    const computedStyle = window.getComputedStyle(grid);
    const gridCols = computedStyle.gridTemplateColumns;
    
    if (width < 768 && gridCols.includes('repeat') && !gridCols.includes('1fr')) {
      issues.push('Grid should collapse to single column on mobile');
    }
  });
  
  // Test font sizes
  const headings = document.querySelectorAll('h1, h2, h3');
  headings.forEach(heading => {
    const computedStyle = window.getComputedStyle(heading);
    const fontSize = parseInt(computedStyle.fontSize);
    
    if (width < 480 && fontSize > 32) {
      issues.push(`Heading font size (${fontSize}px) may be too large for small screens`);
    }
  });
  
  return {
    passed: issues.length === 0,
    issues
  };
};

export const testNavigation = (width) => {
  const issues = [];
  
  const navbar = document.querySelector('.navbar');
  const hamburger = document.querySelector('.hamburger');
  const navMenu = document.querySelector('.nav-menu');
  
  if (!navbar) {
    issues.push('Navbar not found');
    return { passed: false, issues };
  }
  
  // Test mobile navigation
  if (width < 768) {
    if (!hamburger) {
      issues.push('Hamburger menu not found on mobile');
    } else {
      const hamburgerStyle = window.getComputedStyle(hamburger);
      if (hamburgerStyle.display === 'none') {
        issues.push('Hamburger menu should be visible on mobile');
      }
    }
    
    if (navMenu) {
      const menuStyle = window.getComputedStyle(navMenu);
      if (menuStyle.position !== 'fixed' && menuStyle.position !== 'absolute') {
        issues.push('Mobile menu should use fixed or absolute positioning');
      }
    }
  } else {
    // Test desktop navigation
    if (hamburger) {
      const hamburgerStyle = window.getComputedStyle(hamburger);
      if (hamburgerStyle.display !== 'none') {
        issues.push('Hamburger menu should be hidden on desktop');
      }
    }
  }
  
  return {
    passed: issues.length === 0,
    issues
  };
};

export const testContentLayout = (width) => {
  const issues = [];
  
  // Test hero section
  const hero = document.querySelector('.hero');
  if (hero) {
    const heroContent = hero.querySelector('.hero-content');
    if (heroContent) {
      const computedStyle = window.getComputedStyle(heroContent);
      const gridCols = computedStyle.gridTemplateColumns;
      
      if (width < 768 && gridCols.includes('1fr 1fr')) {
        issues.push('Hero content should stack vertically on mobile');
      }
    }
  }
  
  // Test button sizes
  const buttons = document.querySelectorAll('.btn');
  buttons.forEach(button => {
    const computedStyle = window.getComputedStyle(button);
    const minHeight = parseInt(computedStyle.minHeight);
    
    if (width < 768 && minHeight < 44) {
      issues.push(`Button min-height (${minHeight}px) should be at least 44px for touch targets`);
    }
  });
  
  // Test form inputs
  const inputs = document.querySelectorAll('input, textarea');
  inputs.forEach(input => {
    const computedStyle = window.getComputedStyle(input);
    const fontSize = parseInt(computedStyle.fontSize);
    
    if (width < 768 && fontSize < 16) {
      issues.push(`Input font-size (${fontSize}px) should be at least 16px to prevent zoom on iOS`);
    }
  });
  
  return {
    passed: issues.length === 0,
    issues
  };
};

export const testPerformance = () => {
  const issues = [];
  
  // Test image optimization
  const images = document.querySelectorAll('img');
  images.forEach(img => {
    if (!img.loading || img.loading !== 'lazy') {
      issues.push(`Image ${img.src} should have lazy loading`);
    }
    
    if (!img.alt) {
      issues.push(`Image ${img.src} missing alt text`);
    }
  });
  
  // Test CSS animations
  if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    const animatedElements = document.querySelectorAll('[class*="fade"], [class*="slide"]');
    if (animatedElements.length > 0) {
      issues.push('Should respect prefers-reduced-motion setting');
    }
  }
  
  return {
    passed: issues.length === 0,
    issues
  };
};

export const generateTestReport = () => {
  const results = testViewports();
  const summary = {
    total: results.length,
    passed: results.filter(r => r.passed).length,
    failed: results.filter(r => !r.passed).length
  };
  
  console.group('🧪 Cross-Device Compatibility Test Report');
  console.log(`📊 Summary: ${summary.passed}/${summary.total} tests passed`);
  
  results.forEach(result => {
    const icon = result.passed ? '✅' : '❌';
    console.group(`${icon} ${result.device} (${result.viewport})`);
    
    if (!result.passed) {
      if (result.error) {
        console.error('Error:', result.error);
      } else {
        ['responsive', 'navigation', 'layout', 'performance'].forEach(test => {
          if (result[test] && !result[test].passed) {
            console.warn(`${test} issues:`, result[test].issues);
          }
        });
      }
    }
    
    console.groupEnd();
  });
  
  console.groupEnd();
  return { summary, results };
};
