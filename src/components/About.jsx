import React from 'react';
import resumeData from '../services/resumeData.jsx';

const About = () => {
  const basics = resumeData.getBasics();
  const linkedInProfile = basics.profiles.find(profile => profile.network === 'LinkedIn');
  
  // LinkedIn-style value propositions and social proof
  const valueProps = [
    {
      icon: '🚀',
      title: 'Innovation Driver',
      description: 'Led FICO\'s $10M+ analytics platform transformation with 70% faster time-to-market'
    },
    {
      icon: '☁️',
      title: 'Cloud Expert',
      description: 'AWS Partner Cloud Architect with 15+ years in enterprise cloud solutions'
    },
    {
      icon: '🤖',
      title: 'AI Integration',
      description: 'Implementing cutting-edge AI/ML solutions for Fortune 500 companies'
    },
    {
      icon: '🎯',
      title: 'Results Focused',
      description: 'Reduced infrastructure costs by 20% while improving system performance'
    }
  ];

  const testimonials = [
    {
      quote: "<PERSON> delivered exceptional results on our cloud transformation, reducing costs by 20% while improving performance.",
      author: "Enterprise Client",
      role: "Fortune 500 Company"
    },
    {
      quote: "His expertise in AI/ML integration helped us unlock new business opportunities worth millions.",
      author: "Technology Partner",
      role: "Cloud Solutions Provider"
    }
  ];
  
  return (
    <section id="about" className="about" aria-labelledby="about-title">
      <div className="container">
        <h2 id="about-title" className="section-title">About Me</h2>
        <div className="about-content">
          <div className="about-intro">
            <p className="about-text">
              With 15+ years of experience in cloud and platform solutions, I specialize in transforming 
              complex technical challenges into measurable business value. My expertise spans AI/ML integration, 
              multi-cloud architectures, and enterprise modernization initiatives.
            </p>
            <p className="about-highlight">
              🏆 <strong>Red Hat Innovation Award Winner</strong> • Recognized for delivering $10M+ in business value 
              through innovative cloud solutions
            </p>
          </div>

          {/* Value Propositions Grid */}
          <div className="value-props-grid">
            {valueProps.map((prop, index) => (
              <div key={index} className="value-prop-card">
                <span className="value-prop-icon">{prop.icon}</span>
                <h3 className="value-prop-title">{prop.title}</h3>
                <p className="value-prop-description">{prop.description}</p>
              </div>
            ))}
          </div>

          {/* Social Proof / Testimonials */}
          <div className="testimonials-section">
            <h3 className="testimonials-title">What Clients Say</h3>
            <div className="testimonials-grid">
              {testimonials.map((testimonial, index) => (
                <div key={index} className="testimonial-card">
                  <p className="testimonial-quote">"{testimonial.quote}"</p>
                  <div className="testimonial-author">
                    <span className="author-name">{testimonial.author}</span>
                    <span className="author-role">{testimonial.role}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>


        </div>
      </div>
    </section>
  );
};

export default About;