
import React, { useState, useEffect } from 'react';
import FadeInUp from './FadeInUp.jsx';
import resumeData from '../services/resumeData.jsx';
import { FaCloud, FaCogs, FaServer, FaBrain, FaCode, FaChartLine, FaSitemap, FaTools } from 'react-icons/fa';

const Skills = () => {
  const skills = resumeData.getFormattedSkills();
  const [isDesktop, setIsDesktop] = useState(false);
  const [isCondensed, setIsCondensed] = useState(true);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsDesktop(window.innerWidth >= 1024);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  const categoryIcons = {
    "Cloud & Virtualization": <FaCloud />,
    "DevOps & Containerization": <FaCogs />,
    "Operating Systems & Infrastructure": <FaServer />,
    "AI & Data": <FaBrain />,
    "Software Development & Methodologies": <FaCode />,
    "Business & Strategy": <FaChartLine />,
    "Architecture & Core Engineering": <FaSitemap />,
    "Tools & Other": <FaTools />,
  };

  // Condensed desktop layout
  const renderCondensedLayout = () => (
    <div className="skills-condensed">
      {Object.keys(skills).map((category, index) => (
        <FadeInUp key={index} delay={index * 100}>
          <div className="skill-category-condensed">
            <h3 className="skill-category-title-condensed">
              {categoryIcons[category]}
              <span>{category}</span>
            </h3>
            <div className="skill-list-condensed">
              {skills[category].map((skill, i) => (
                <span className="skill-tag-condensed" key={i}>{skill}</span>
              ))}
            </div>
          </div>
        </FadeInUp>
      ))}
    </div>
  );

  // Standard mobile/tablet layout
  const renderStandardLayout = () => (
    <div className="skills-grid">
      {Object.keys(skills).map((category, index) => (
        <FadeInUp key={index}>
          <div className="skill-category">
            <h3 className="skill-category-title">
              {categoryIcons[category]} {category}
            </h3>
            <div className="skill-list">
              {skills[category].map((skill, i) => (
                <span className="skill-tag" key={i}>{skill}</span>
              ))}
            </div>
          </div>
        </FadeInUp>
      ))}
    </div>
  );

  return (
    <section id="skills" className="skills">
      <div className="container">
        <div className="skills-header">
          <h2 className="section-title">Technical Skills</h2>
          {isDesktop && (
            <div className="skills-toggle">
              <button
                className={`toggle-btn ${isCondensed ? 'active' : ''}`}
                onClick={() => setIsCondensed(true)}
                aria-label="Switch to condensed view"
              >
                Condensed
              </button>
              <button
                className={`toggle-btn ${!isCondensed ? 'active' : ''}`}
                onClick={() => setIsCondensed(false)}
                aria-label="Switch to expanded view"
              >
                Expanded
              </button>
            </div>
          )}
        </div>
        {isDesktop && isCondensed ? renderCondensedLayout() : renderStandardLayout()}
      </div>
    </section>
  );
};

export default Skills;
