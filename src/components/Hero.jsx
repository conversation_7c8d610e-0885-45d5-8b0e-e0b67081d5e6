import React from 'react';
import FadeInUp from './FadeInUp.jsx';
import LazyImage from './LazyImage.jsx';
import resumeData from '../services/resumeData.jsx';

const Hero = () => {
  const basics = resumeData.getBasics();
  const linkedInProfile = basics.profiles.find(profile => profile.network === 'LinkedIn');
  
  // Extract keywords for LinkedIn-style headline
  const keywords = ['Cloud Architecture', 'AI/ML Integration', 'Platform Engineering', 'Digital Transformation'];
  
  return (
    <section id="hero" className="hero">
      {/* LinkedIn-style banner background */}
      <div className="hero-banner">
        <div className="banner-pattern"></div>
      </div>
      
      <div className="hero-container">
        <div className="hero-content">
          <div className="hero-text">

            <FadeInUp delay={0.1}>
              <h1 className="hero-title">{basics.name}</h1>
            </FadeInUp>
            <FadeInUp delay={0.2}>
              <h2 className="hero-headline">
                {basics.label.split('|')[0].trim()} • {keywords.slice(0, 3).join(' • ')}
              </h2>
            </FadeInUp>
            <FadeInUp delay={0.3}>
              <p className="hero-value-prop">
                Driving $10M+ in business value through innovative cloud solutions and AI integration
              </p>
            </FadeInUp>
            <FadeInUp delay={0.4}>
              <div className="hero-stats">
                <div className="stat-item">
                  <span className="stat-number">15+</span>
                  <span className="stat-label">Years Experience</span>
                </div>
                <div className="stat-item">
                  <span className="stat-number">$10M+</span>
                  <span className="stat-label">Business Value</span>
                </div>
                <div className="stat-item">
                  <span className="stat-number">70%</span>
                  <span className="stat-label">Faster Delivery</span>
                </div>
              </div>
            </FadeInUp>
            <FadeInUp delay={0.6}>
              <div className="hero-buttons">
                <a href="#contact" className="btn btn--primary">
                  <span>Start a Conversation</span>
                  <span className="btn-icon">→</span>
                </a>
                <a href="#featured" className="btn btn--outline">View Highlights</a>
                {linkedInProfile && (
                  <a href={linkedInProfile.url} target="_blank" rel="noopener noreferrer" className="btn btn--linkedin">
                    <span>LinkedIn Profile</span>
                    <span className="btn-icon">↗</span>
                  </a>
                )}
              </div>
            </FadeInUp>
          </div>
          <div className="hero-image">
            <FadeInUp delay={0.8}>
              <div className="hero-image-container">
                <div className="image-frame">
                  <LazyImage
                    src="/SCR-20250626-jqdc.png"
                    alt="Nicholas Gerasimatos - Cloud Architecture Expert"
                    className="profile-image"
                    placeholder={
                      <div style={{
                        width: '100%',
                        height: '100%',
                        backgroundColor: 'var(--color-surface)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'var(--color-text-secondary)',
                        fontSize: 'var(--font-size-sm)'
                      }}>
                        Loading...
                      </div>
                    }
                  />
                </div>
                <div className="image-badge">
                  <span className="badge-text">🏆 Award Winner</span>
                </div>
              </div>
            </FadeInUp>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;