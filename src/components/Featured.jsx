import React from 'react';
import FadeInUp from './FadeInUp.jsx';
import resumeData from '../services/resumeData.jsx';

const Featured = () => {
  const awards = resumeData.getAwards();
  const certificates = resumeData.getCertificates();
  const basics = resumeData.getBasics();

  // Featured content based on LinkedIn recommendations
  const featuredItems = [
    {
      type: 'achievement',
      title: 'Red Hat Innovation Award Winner',
      description: 'Recognized for outstanding achievements in open source with FICO\'s $10M+ analytics platform',
      icon: '🏆',
      link: '#awards',
      highlight: 'Reduced infrastructure costs by 20% and improved time to market by 70%'
    },
    {
      type: 'certification',
      title: 'AWS Solutions Architect',
      description: 'Current AWS certification demonstrating cloud architecture expertise',
      icon: '☁️',
      link: certificates.find(cert => cert.name.includes('AWS'))?.url || '#certificates',
      highlight: 'Valid through 2025'
    },
    {
      type: 'expertise',
      title: 'AI/ML Integration Specialist',
      description: 'Leading AI-driven solutions and automation implementations',
      icon: '🤖',
      link: '#experience',
      highlight: 'Implementing cutting-edge AI solutions at AHEAD'
    },
    {
      type: 'thought-leadership',
      title: 'OpenStack Speakers Bureau',
      description: 'Published articles and industry conference presentations',
      icon: '🎤',
      link: '#publications',
      highlight: 'Recognized thought leader in cloud technologies'
    }
  ];

  return (
    <section id="featured" className="featured" aria-labelledby="featured-title">
      <div className="container">
        <FadeInUp>
          <h2 id="featured-title" className="section-title">Featured Highlights</h2>
          <p className="section-subtitle">Key achievements and expertise that drive results</p>
        </FadeInUp>
        
        <div className="featured-grid">
          {featuredItems.map((item, index) => (
            <FadeInUp key={index} delay={0.1 * (index + 1)}>
              <div className="featured-card">
                <div className="featured-card-header">
                  <span className="featured-icon">{item.icon}</span>
                  <div className="featured-badge">{item.type}</div>
                </div>
                <h3 className="featured-title">{item.title}</h3>
                <p className="featured-description">{item.description}</p>
                <div className="featured-highlight">
                  <span className="highlight-text">{item.highlight}</span>
                </div>
                <a href={item.link} className="featured-link">
                  Learn More →
                </a>
              </div>
            </FadeInUp>
          ))}
        </div>

        {/* Social Proof Section */}
        <FadeInUp delay={0.6}>
          <div className="social-proof">
            <h3 className="social-proof-title">Trusted by Industry Leaders</h3>
            <div className="company-logos">
              <div className="company-logo">
                <img src="/Logos/Amazon_Web_Services-Logo.wine.svg" alt="AWS" className="logo-image" />
              </div>
              <div className="company-logo">
                <img src="/Logos/Red_Hat-Logo.wine.svg" alt="Red Hat" className="logo-image" />
              </div>
              <div className="company-logo">
                <img src="/Logos/Microsoft-Logo.wine.svg" alt="Microsoft" className="logo-image" />
              </div>
              <div className="company-logo">
                <img src="/Logos/FICO_logo.svg.png" alt="FICO" className="logo-image" />
              </div>
              <div className="company-logo">
                <img src="/Logos/American_Express-Logo.wine.svg" alt="American Express" className="logo-image" />
              </div>
              <div className="company-logo">
                <img src="/Logos/JPMorgan_Chase-Logo.wine.svg" alt="JPMorgan Chase" className="logo-image" />
              </div>
              <div className="company-logo">
                <img src="/Logos/PayPal-Logo.wine.svg" alt="PayPal" className="logo-image" />
              </div>
              <div className="company-logo">
                <img src="/Logos/Visa_Inc.-Logo.wine.svg" alt="Visa" className="logo-image" />
              </div>
              <div className="company-logo">
                <img src="/Logos/Canon_Inc.-Logo.wine.svg" alt="Canon" className="logo-image" />
              </div>
            </div>
          </div>
        </FadeInUp>
      </div>
    </section>
  );
};

export default Featured;