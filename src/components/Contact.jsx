
import React, { useState } from 'react';
import { useMobileDetection } from '../hooks/useMobileGestures.jsx';
import resumeData from '../services/resumeData.jsx';

const Contact = () => {
  const basics = resumeData.getBasics();
  const { isMobile } = useMobileDetection();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({ ...prevData, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus(null);

    try {
      // Simulate form submission
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Here you would typically send the form data to your backend
      console.log('Form submitted:', formData);

      setSubmitStatus('success');
      setFormData({ name: '', email: '', subject: '', message: '' });

      // Show success message
      if (isMobile) {
        // Provide haptic feedback on mobile if available
        if (navigator.vibrate) {
          navigator.vibrate(100);
        }
      }
    } catch (error) {
      setSubmitStatus('error');
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id="contact" className="contact">
      <div className="container">
        <h2 className="section-title">Get In Touch</h2>
        <div className="contact-content">
          <div className="contact-info-section">
            <h3>Contact Information</h3>
            <div className="contact-details">
              <div className="contact-detail">
                <strong>Location:</strong> {basics.location?.city}, {basics.location?.region}, {basics.location?.countryCode}
              </div>
              <div className="contact-detail">
                <strong>LinkedIn:</strong> <a href={basics.profiles?.find(p => p.network === 'LinkedIn')?.url} target="_blank" rel="noopener noreferrer">{basics.profiles?.find(p => p.network === 'LinkedIn')?.url}</a>
              </div>
            </div>
          </div>
          <div className="contact-form-section">
            <h3>Send a Message</h3>
            <form className="contact-form" id="contact-form" onSubmit={handleSubmit}>
              <div className="form-group">
                <label htmlFor="name" className="form-label">Name</label>
                <input type="text" id="name" name="name" className="form-control" required value={formData.name} onChange={handleChange} />
              </div>
              <div className="form-group">
                <label htmlFor="email" className="form-label">Email</label>
                <input type="email" id="email" name="email" className="form-control" required value={formData.email} onChange={handleChange} />
              </div>
              <div className="form-group">
                <label htmlFor="subject" className="form-label">Subject</label>
                <input type="text" id="subject" name="subject" className="form-control" required value={formData.subject} onChange={handleChange} />
              </div>
              <div className="form-group">
                <label htmlFor="message" className="form-label">Message</label>
                <textarea id="message" name="message" rows="5" className="form-control" required value={formData.message} onChange={handleChange}></textarea>
              </div>
              <button
                type="submit"
                className={`btn btn--primary btn--full-width ${isSubmitting ? 'btn--loading' : ''}`}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <span className="btn-spinner"></span>
                    Sending...
                  </>
                ) : (
                  'Send Message'
                )}
              </button>

              {submitStatus === 'success' && (
                <div className="form-message form-message--success">
                  ✅ Thank you! Your message has been sent successfully.
                </div>
              )}

              {submitStatus === 'error' && (
                <div className="form-message form-message--error">
                  ❌ Sorry, there was an error sending your message. Please try again.
                </div>
              )}
            </form>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
