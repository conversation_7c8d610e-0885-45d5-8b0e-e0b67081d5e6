{"timestamp": "2025-06-27T14:09:42.233Z", "tests": [{"name": "CSS Responsiveness", "passed": false, "issues": ["Consider using more mobile-first media queries", "Missing accessibility pattern: aria-"], "mediaQueries": 36, "mobileFirstQueries": 21}, {"name": "Component Responsiveness", "passed": false, "issues": ["Component Contact.jsx missing accessibility attributes", "Component DeviceTestRunner.jsx missing accessibility attributes", "Component Education.jsx missing accessibility attributes", "Component FadeInUp.jsx missing accessibility attributes", "Component Footer.jsx missing accessibility attributes", "Component Publications.jsx missing accessibility attributes"], "componentCount": 14, "responsiveHooksUsage": 5}, {"name": "Performance Optimizations", "passed": true, "issues": []}], "summary": {"total": 3, "passed": 1, "failed": 2}}