import resumeData from './src/services/resumeData.jsx';
import fs from 'fs';

// Load the resume data
const skills = resumeData.getFormattedSkills();

console.log('=== SKILLS DEBUG ===');
console.log('Total categories:', Object.keys(skills).length);
console.log('Categories:', Object.keys(skills));

Object.keys(skills).forEach(category => {
  console.log(`\n${category} (${skills[category].length} skills):`);
  skills[category].forEach(skill => {
    console.log(`  - ${skill}`);
  });
});

console.log('\n=== RAW RESUME DATA ===');
console.log('Total skills in resume:', resumeData.data.skills.length);
resumeData.data.skills.forEach(skill => {
  console.log(`- ${skill.name}`);
});